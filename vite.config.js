import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@store': path.resolve(__dirname, './src/store'),
      '@api': path.resolve(__dirname, './src/api'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@assets': path.resolve(__dirname, './src/assets'),
    },
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'https://hijoy.me/',
        rewrite: (path) => path.replace(/^\/api/, ''),
        changeOrigin: true,
        secure: false, // 如果是 https 但证书不受信任，设为 false
        configure: (proxy, options) => {
          // 打印代理请求信息
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxy request headers before modification:', proxyReq.getHeaders());

            // 确保 Content-Type 被保留
            if (req.body) {
              proxyReq.setHeader('Content-Type', 'application/json');

              // 如果是空对象，确保正确序列化
              if (typeof req.body === 'object' && Object.keys(req.body).length === 0) {
                const bodyData = JSON.stringify({});
                proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
                // 写入请求体
                proxyReq.write(bodyData);
              }
            }

            console.log('Proxy request headers after modification:', proxyReq.getHeaders());
          });

          // 记录代理响应
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Proxy response status:', proxyRes.statusCode);
            console.log('Proxy response headers:', proxyRes.headers);
          });

          // 记录代理错误
          proxy.on('error', (err, req, res) => {
            console.error('Proxy error:', err);
          });
        },
        headers: {
          'Content-Type': 'application/json',
        },
      }
    }
  },
});