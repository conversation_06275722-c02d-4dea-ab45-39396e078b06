{"name": "snap-link", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,css,md}\""}, "dependencies": {"@headlessui/react": "1.7.17", "@heroicons/react": "2.0.18", "axios": "^1.8.4", "clsx": "2.0.0", "crypto-js": "^4.2.0", "date-fns": "2.30.0", "framer-motion": "10.16.5", "react": "18.2.0", "react-dom": "18.2.0", "react-dropzone": "14.2.3", "react-error-boundary": "4.0.11", "react-hook-form": "7.48.2", "react-hot-toast": "2.4.1", "react-query": "3.39.3", "react-router-dom": "6.18.0", "zustand": "4.4.6"}, "devDependencies": {"@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/forms": "0.5.7", "@types/node": "20.9.0", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitejs/plugin-react": "4.1.1", "autoprefixer": "10.4.16", "eslint": "8.53.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-import": "2.29.0", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-prettier": "5.0.1", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.4", "postcss": "8.4.31", "prettier": "3.1.0", "tailwindcss": "3.3.5", "vite": "^4.5.13"}}