# Upload 功能修复方案

## 1. CORS 错误修复 ✅

修复了 AWS S3 预签名 URL 上传时的 CORS 错误。

### 问题分析：

AWS S3 预签名 URL 不支持 FormData 格式上传，需要使用原始文件数据。

### 主要变更：

- 将 FormData 上传改为原始文件数据上传
- 添加正确的 Content-Type 和 Content-MD5 头部
- 修复了 `directUpload` 函数和头像上传功能

### 代码变更：

```javascript
// 旧代码 (FormData - 会导致 CORS 错误)
const formData = new FormData();
formData.append('file', file);
fetch(url, {
  method: 'PUT',
  body: formData,
});

// 新代码 (原始文件数据 - 正确方式)
fetch(url, {
  method: 'PUT',
  body: file,
  headers: {
    'Content-Type': file.type,
    'Content-MD5': contentMd5,
  },
});
```

## 2. XHR 改为 Fetch API ✅

已将 `src/utils/uploadHelpers.js` 中的 `directUpload` 函数从 XMLHttpRequest 改为 fetch API。

## 2. 服务端连接错误解决方案

### 错误分析：

```
"transport: Error while dialing: dial tcp: address http://snaplink:9000: too many colons in address"
```

这个错误表明服务端配置中使用了完整的 URL（包含 `http://`），但应该只使用主机名和端口。

### 可能的原因和解决方案：

#### A. 服务端配置问题（最可能）

服务端的某个配置文件中，连接地址配置错误：

**错误配置：**

```
MINIO_ENDPOINT=http://snaplink:9000
# 或
S3_ENDPOINT=http://snaplink:9000
```

**正确配置：**

```
MINIO_ENDPOINT=snaplink:9000
# 或
S3_ENDPOINT=snaplink:9000
```

#### B. Docker Compose 配置问题

如果使用 Docker，检查 `docker-compose.yml` 中的服务配置：

**可能的问题配置：**

```yaml
services:
  api:
    environment:
      - STORAGE_ENDPOINT=http://snaplink:9000 # 错误
```

**正确配置：**

```yaml
services:
  api:
    environment:
      - STORAGE_ENDPOINT=snaplink:9000 # 正确
```

#### C. 环境变量配置问题

检查服务端的环境变量配置文件（如 `.env`）：

**错误：**

```
MINIO_ENDPOINT=http://snaplink:9000
AWS_ENDPOINT=http://snaplink:9000
```

**正确：**

```
MINIO_ENDPOINT=snaplink:9000
AWS_ENDPOINT=snaplink:9000
```

### 具体修复步骤：

1. **检查服务端配置文件**

   - 查找包含 `snaplink:9000` 的配置
   - 移除 `http://` 前缀
   - 只保留主机名和端口

2. **检查 Docker 配置**

   - 检查 `docker-compose.yml`
   - 检查 Dockerfile 中的环境变量

3. **检查应用配置**

   - 检查服务端的配置文件（如 `config.yaml`, `application.properties` 等）
   - 检查环境变量设置

4. **重启服务**
   - 修改配置后重启相关服务
   - 清除可能的缓存

### 常见配置文件位置：

- `docker-compose.yml`
- `.env` 文件
- `config/app.yaml`
- `application.properties`
- Kubernetes ConfigMap

### 验证修复：

修复后，错误信息应该消失，上传功能应该正常工作。

## 3. 测试建议

### 前端测试：

1. 测试图片直接上传（使用新的 fetch API）
2. 测试视频多部分上传
3. 测试进度跟踪功能
4. 测试错误处理

### 后端测试：

1. 检查存储服务连接
2. 验证预签名 URL 生成
3. 测试文件上传到存储服务

## 4. 监控和日志

建议添加更详细的日志来监控上传过程：

```javascript
// 在 directUpload 函数中添加
console.log('Upload URL:', url);
console.log('Upload method:', method);
console.log('File info:', { name: file.name, size: file.size, type: file.type });
```

这样可以更好地诊断问题和监控上传性能。
