---
description: 
globs: 
alwaysApply: false
---
# Component Structure Guide
Components should follow these guidelines:

## File Organization
- Each component should be in its own directory under `src/components/`
- Component files should use `.jsx` extension
- Related components should be grouped in subdirectories

## Component Structure
1. Imports (React, hooks, utilities)
2. Component definition
3. PropTypes/TypeScript types
4. Exports

## Best Practices
- Use functional components with hooks
- Implement error boundaries for critical components
- Use React.memo() for performance optimization when needed
- Follow the single responsibility principle
