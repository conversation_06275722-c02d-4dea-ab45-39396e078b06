---
description: 
globs: 
alwaysApply: false
---
# State Management Guide
The application uses Zustand for state management.

## Store Structure
- Store files are located in `src/store/`
- Each store should be focused on a specific domain
- Use TypeScript for better type safety

## Best Practices
- Keep stores small and focused
- Use selectors for derived state
- Implement proper error handling
- Use middleware for side effects
