---
description: 
globs: 
alwaysApply: false
---
# API Integration Guide
The project uses Axios and React Query for API integration.

## Structure
- API endpoints are defined in `src/api/`
- Use React Query hooks for data fetching
- Implement proper error handling

## Best Practices
- Use React Query for server state management
- Implement proper loading and error states
- Use TypeScript for API response types
- Follow RESTful conventions
