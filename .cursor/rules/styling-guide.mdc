---
description: 
globs: 
alwaysApply: false
---
# Styling Guide
The project uses TailwindCSS for styling.

## Configuration
- Tailwind configuration is in `tailwind.config.js`
- Custom styles can be added in `src/styles/`

## Best Practices
- Use Tailwind utility classes as the primary styling method
- Create custom components for repeated patterns
- Use the `clsx` utility for conditional classes
- Follow mobile-first responsive design
