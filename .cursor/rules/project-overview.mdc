---
description: 
globs: 
alwaysApply: false
---
# Project Overview
This is a React-based web application built with Vite, using modern tooling and best practices.

## Tech Stack
- React 18 with Vite as the build tool
- TailwindCSS for styling
- Zustand for state management
- React Query for data fetching
- React Router for routing
- React Hook Form for form handling
- Framer Motion for animations

## Key Directories
- `src/components/` - Reusable UI components
- `src/pages/` - Page components and routes
- `src/hooks/` - Custom React hooks
- `src/utils/` - Utility functions
- `src/api/` - API integration code
- `src/store/` - Zustand store definitions
- `src/styles/` - Global styles and Tailwind configuration

## Development Workflow
1. Run `npm run dev` to start the development server
2. Use `npm run lint` to check for code issues
3. Use `npm run format` to format code with Prettier
4. Use `npm run build` to create a production build
