import { format, formatDistance } from 'date-fns';

// Format date in a readable format
export const formatDate = (date) => {
  if (!date) return '';
  return format(new Date(date), 'MMM dd, yyyy');
};

// Format date with time
export const formatDateTime = (date) => {
  if (!date) return '';
  return format(new Date(date), 'MMM dd, yyyy HH:mm');
};

// Format relative time (e.g., "2 hours ago")
export const formatRelativeTime = (date) => {
  if (!date) return '';
  return formatDistance(new Date(date), new Date(), { addSuffix: true });
};

// Format currency
export const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

// Format file size
export const formatFileSize = (bytes) => {
  // Add robust input validation
  if (typeof bytes !== 'number' || isNaN(bytes) || bytes < 0) {
    // Behavior for invalid input can be customized (e.g., return '0 Bytes', '', or throw error)
    // For now, returning a descriptive string.
    // Consider if '0 Bytes' or an empty string is more appropriate for UI display.
    return 'Invalid file size'; 
  }

  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']; // Add more units like PB, EB if needed
  
  // Calculate the index 'i' for the 'sizes' array
  // Math.log(bytes) / Math.log(k) gives the power to which k must be raised to get bytes.
  // E.g., for 1KB (1024 bytes), i = log(1024)/log(1024) = 1. sizes[1] is 'KB'.
  // E.g., for 500 bytes, i = log(500)/log(1024) approx 0.89. floor(0.89) = 0. sizes[0] is 'Bytes'.
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  // Handle cases where bytes is between 0 and 1 (exclusive of 0), which results in i < 0.
  // In such cases, unit should be 'Bytes' (index 0).
  // Also, cap 'i' if it exceeds the largest unit in 'sizes' array (e.g., for extremely large files).
  const unitIndex = Math.max(0, Math.min(i, sizes.length - 1));

  // Calculate the value in the chosen unit
  const value = bytes / Math.pow(k, unitIndex);
  
  // Format the value to 2 decimal places and append the unit
  return `${parseFloat(value.toFixed(2))} ${sizes[unitIndex]}`;
};

// Truncate text with ellipsis
export const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) return text;
  return `${text.slice(0, maxLength)}...`;
};