const errorMessages = {
  0: '未知错误，请稍后重试。',
  10000: '操作成功。',
  10001: '图形验证码错误。',
  10002: '发送邮件频率过快，请稍后再试。',
  10003: '短信验证码已过期。',
  10004: '短信验证码错误。',
  10005: '资源状态异常。',
  10006: '分片状态异常。',
  10007: '更新冲突，请刷新后重试。',
  10008: '权限不足。',
  10009: '余额不足。',
  10010: '支付订单状态异常。',
  10011: '参数非法。',
  10012: '租户不存在。',
  10013: 'Collection 不存在。',
  10014: '用户已存在。',
  10015: '密码错误。',
  10016: '数据不存在。',
  // 可以根据需要添加更多错误码和对应的中文提示
};

export const getErrorMessage = (error) => {
  if (error && error.response && error.response.data && error.response.data.code !== undefined) {
    return errorMessages[error.response.data.code] || error.response.data.message || '发生未知错误，请稍后重试。';
  }
  if (error && error.message) {
    return error.message;
  }
  return '发生未知错误，请稍后重试。';
};

export default errorMessages;