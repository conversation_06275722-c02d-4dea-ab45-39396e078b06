// Token management utilities
export const setTokens = (tokens) => {
  if (tokens?.accessToken) {
    console.log('Setting access token:', tokens.accessToken);
    localStorage.setItem('accessToken', tokens.accessToken);
  }
};

export const getAccessToken = () => {
  return localStorage.getItem('accessToken');
};

export const clearTokens = () => {
  localStorage.removeItem('accessToken');
};

// Password validation
export const validatePassword = (password) => {
  // 6-16 位字母、数字或下划线
  const regex = /^[a-zA-Z0-9_]{6,16}$/;
  return regex.test(password);
};

// Email validation
export const validateEmail = (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};