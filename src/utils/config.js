export const getStoragePrefix = () => {
  return import.meta.env.VITE_STORAGE_PREFIX || 'snap_link_';
};

export const getApiUrl = () => {
  return import.meta.env.VITE_API_BASE_URL;
};

export const getAppName = () => {
  return import.meta.env.VITE_APP_NAME || 'Snap-Link';
};

export const RESOURCE_CATEGORIES = [
  'images',
  'videos',
  'audio',
  'documents',
  'templates',
  'other',
];

export const FILE_SIZE_LIMITS = {
  image: 10 * 1024 * 1024, // 10MB
  video: 100 * 1024 * 1024, // 100MB
  audio: 50 * 1024 * 1024, // 50MB
  document: 25 * 1024 * 1024, // 25MB
  default: 50 * 1024 * 1024, // 50MB
};

export const ACCEPTED_FILE_TYPES = {
  image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  video: ['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska'],
  audio: ['audio/mpeg', 'audio/ogg', 'audio/wav'],
  document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
};

// Combined accepted file types for auto-detection
export const ALL_ACCEPTED_FILE_TYPES = {
  'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
  'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm'],
};