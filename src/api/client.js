import axios from 'axios';

import { getAccessToken, clearTokens, setTokens } from '@utils/auth';

// 创建一个默认的请求头配置
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};

const apiClient = axios.create({
  baseURL: '/api', // 使用相对路径，并指向 Vite 代理
  headers: defaultHeaders,
});

// 确保空对象被正确序列化为 JSON
const jsonRequestTransformer = (data, headers) => {
  if (data === undefined || data === null) {
    return JSON.stringify({});
  }

  // 如果已经是字符串，不需要再次转换
  if (typeof data === 'string') {
    return data;
  }

  // 确保设置正确的 Content-Type
  headers['Content-Type'] = 'application/json';

  return JSON.stringify(data);
};

// 配置默认的请求转换器
apiClient.defaults.transformRequest = [jsonRequestTransformer];

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    console.log('Request token:', token);
    if (token) {
      config.headers.Authorization = `${token}`;
    }

    // 确保 Content-Type 被正确设置，并且不会被覆盖
    config.headers['Content-Type'] = 'application/json';

    // 如果请求体是空对象，确保它被正确序列化
    if (config.data && typeof config.data === 'object' && Object.keys(config.data).length === 0) {
      config.data = JSON.stringify({});
    }

    // 打印完整的请求信息
    console.log('Request headers:', JSON.stringify(config.headers));
    console.log('Request method:', config.method);
    console.log('Request URL:', config.url);
    console.log('Request data:', config.data);

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    // 记录响应头，以便调试
    console.log('Response headers:', response.headers);

    if (response.data && response.data.code !== 10000) {
      console.log('Response data:', response);
      const error = new Error(response.data.message || '业务逻辑错误，请稍后重试。');
      error.isAxiosError = true; // Mimic Axios error structure
      error.response = response; // Attach the full response
      error.config = response.config; // Attach the request config

      // // 如果是认证相关的错误码（例如：令牌失效），可以在这里处理
      // // 例如，如果后端返回特定的错误码表示令牌失效
      // if (response.data.code === 10001 || response.data.code === 10002) { // 假设10001和10002是认证相关错误码
      //   clearTokens();
      //   window.location.href = '/login';
      // }

      return Promise.reject(error);
    }
    return response;
  },
  async (error) => {
    // 记录错误信息，包括请求配置
    console.error('API Error:', error);
    if (error.config) {
      console.error('Failed request config:', {
        url: error.config.url,
        method: error.config.method,
        headers: error.config.headers,
        data: error.config.data
      });
    }

    const originalRequest = error.config;
    return Promise.reject(error);
  }
);

export default apiClient;