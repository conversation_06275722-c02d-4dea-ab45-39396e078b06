import apiClient from './client';
import { setTokens } from '@utils/auth';

// Authentication API routes

/**
 * Logs in a user.
 * @param {object} credentials - The login credentials.
 * @param {string} credentials.email - User's email.
 * @param {string} credentials.tenant_id - Tenant ID (e.g., "SnapLink").
 * @param {string} credentials.password - User's password.
 * @returns {Promise<object>} The login reply, containing an access token.
 * @returns {Promise<{access_token: string}>} The login reply.
 */
export const login = async (credentials) => {
  const { data } = await apiClient.post('/uc/v1/account/login', credentials);
  // The interceptor already checks for code: 10000
  if (data && data.data && data.data.access_token) {
    setTokens({ accessToken: data.data.access_token });
    await getCurrentUser(); 
  }
  return data;
};

/**
 * Registers a new user.
 * @param {object} userData - The user registration data.
 * @param {string} userData.tenant_id - Tenant ID (e.g., "SnapLink").
 * @param {string} userData.email - User's email.
 * @param {string} userData.main_code - Email verification code.
 * @param {string} userData.password - User's password.
 * @returns {Promise<object>} The login reply, containing an access token.
 * @returns {Promise<{access_token: string}>} The login reply.
 */
export const register = async (userData) => {
  const { data } = await apiClient.post('/uc/v1/account/register', userData);
  // Assuming the response structure is { code, msg, reason, data: { access_token } }
  // The interceptor already checks for code: 10000
  if (data && data.data && data.data.access_token) {
    setTokens({ accessToken: data.data.access_token });
    await getCurrentUser();
  }
  return data;
};

/**
 * Sets a new password after verifying a code from the forgot password flow.
 * @param {object} payload - The payload for resetting the password.
 * @param {string} payload.tenant_id - Tenant ID (e.g., "SnapLink").
 * @param {string} payload.email - User's email.
 * @param {string} payload.main_code - Email verification code.
 * @param {string} payload.password - The new password.
 * @returns {Promise<object>} The login reply, containing an access token.
 * @returns {Promise<{access_token: string}>} The login reply.
 */
export const setNewPasswordAfterCode = async (payload) => {
  const { data } = await apiClient.post('/uc/v1/account/password/forget', payload);
  return data;
};


/**
 * Changes the current user's password (when logged in).
 * @param {object} resetData - The password reset data.
 * @param {string} resetData.password_old - The old password.
 * @param {string} resetData.password_new - The new password.
 * @returns {Promise<object>} An empty object on success.
 */
export const resetPassword = async (resetData) => {
  const { data } = await apiClient.post('/uc/v1/account/password/change', resetData);
  return data;
};

/**
 * Gets the current authenticated user's information.
 * @returns {Promise<object>} The user object.
 * @returns {Promise<{id: string, username: string, nickname: string, avatar: string, email: string, created: string}>} User data.
 */
export const getCurrentUser = async () => {
  const { data } = await apiClient.post('/uc/v1/account/get/me',{});
  return data;
};

/**
 * Logs out the current user.
 * @returns {Promise<object>} An empty object on success.
 */
export const logout = async () => {
  const { data } = await apiClient.post('/uc/v1/account/logout');
  return data;
};

/**
 * Sends an email for purposes like registration verification or password reset initiation.
 * @param {object} emailData - The email sending request data.
 * @param {string} emailData.email - Recipient's email.
 * @param {string} emailData.tenant_id - Tenant ID (e.g., "SnapLink").
 * @param {'Register' | 'ForgetPassword'} emailData.type - The type of email to send.
 * @returns {Promise<object>} An empty object on success.
 */
export const sendEmail = async (emailData) => {
  const { data } = await apiClient.post('/uc/v1/account/send_mail', emailData);
  return data;
};

/**
 * Gets the login action type (Login or Register) for a given email and tenant.
 * @param {object} actionData - The data to determine login action.
 * @param {string} actionData.email - User's email.
 * @param {string} actionData.tenant_id - Tenant ID (e.g., "SnapLink").
 * @returns {Promise<object>} The login action reply.
 * @returns {Promise<{action: 'Login' | 'Register'}>} The login action reply.
 */
export const getLoginAction = async (actionData) => {
  const { data } = await apiClient.post('/uc/v1/account/login/action', actionData);
  return data;
};
