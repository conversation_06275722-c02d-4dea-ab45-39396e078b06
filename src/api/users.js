import apiClient from './client';

/**
 * Gets the current authenticated user's profile information.
 * Renamed from getUserProfile.
 * @returns {Promise<object>} The user profile data.
 * @returns {Promise<{id: string, username: string, nickname: string, avatar: string, email: string, created: string}>} User data.
 */
export const getMyProfile = async () => {
  const { data } = await apiClient.post('/uc/v1/account/get/me', {});
  return data;
};

/**
 * Changes the current user's information (nickname and/or avatar URL).
 * Renamed from updateUserProfile.
 * The actual avatar file upload should be handled separately using 'uploadAvatarURL' to get a pre-signed URL,
 * then uploading the file, and then calling this function with the resulting avatar string (e.g., object_key or full URL).
 * @param {object} userInfoData - The user information to update.
 * @param {string} [userInfoData.nickname] - The new nickname.
 * @param {string} [userInfoData.avatar] - The new avatar URL/path/key.
 * @returns {Promise<object>} An empty object on success.
 */
export const changeUserInfo = async (userInfoData) => {
  // API expects application/json with avatar as a string (path/URL)
  const payload = {};
  if (userInfoData.nickname !== undefined) {
    payload.nickname = userInfoData.nickname;
  }
  if (userInfoData.avatar !== undefined) {
    payload.avatar = userInfoData.avatar; // This should be a string (URL or key)
  }

  const { data } = await apiClient.post('/uc/v1/account/change', payload);
  return data;
};

/**
 * Gets the current user's assets (balance).
 * Assumes user_id and tenant_id are handled by the backend for HTTP calls as per spec.
 * @returns {Promise<object>} The user assets reply.
 * @returns {Promise<{asset: {available: string, freeze: string}}}>} User assets data.
 */
export const getUserAssets = async () => {
  // API expects empty body for client-side calls as user_id & tenant_id are implicit
  const { data } = await apiClient.post('/uc/v1/account/assets');
  return data;
};

/**
 * Requests a pre-signed URL for uploading an avatar.
 * @param {object} reqData - The request data for the pre-signed URL.
 * @param {string} reqData.filename - The name of the file to be uploaded.
 * @param {string} reqData.content_type - The MIME type of the file.
 * @param {string} reqData.content_md5 - The MD5 hash of the file content.
 * @param {string} reqData.content_size - The size of the file in bytes.
 * @returns {Promise<object>} The pre-signed URL reply.
 * @returns {Promise<{url: string, method: string, object_key: string}>} Pre-signed URL data.
 */
export const uploadAvatarURL = async (reqData) => {
  const { data } = await apiClient.post('/uc/v1/account/avatar/url', reqData);
  return data;
};

/**
 * Deletes the current user's account.
 * @returns {Promise<object>} An empty object on success.
 */
export const deleteAccount = async () => {
  const { data } = await apiClient.post('/uc/v1/account/delete');
  return data;
};

/**
 * Pays for an order.
 * @param {object} reqData - The request data for paying an order.
 * @param {string} reqData.order_id - The ID of the order to pay.
 * @returns {Promise<object>} An empty object on success (as per spec account.v1.PayOrderReply: {}).
 */
export const payOrder = async (reqData) => {
  const { data } = await apiClient.post('/uc/v1/account/order/pay', reqData); return data;
};