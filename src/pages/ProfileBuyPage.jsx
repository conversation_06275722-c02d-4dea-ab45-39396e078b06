import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import useAuth from '@hooks/useAuth';
import { getMyOrders } from '@api/snaplink';

const ProfileBuyPage = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const isCurrentUser = !id || id === user?.id;
  const [orders, setOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(10);

  useEffect(() => {
    const fetchOrders = async () => {
      setIsLoading(true);
      setError(null);
      try {
        let data;
        if (isCurrentUser) {
          data = await getMyOrders({ page: currentPage, size: pageSize });
        } else {
          // 仅允许本人查看
          setOrders([]);
          setError('Only the current user can view their purchased collections.');
          setIsLoading(false);
          return;
        }
        setOrders(data.orders || []);
      } catch (err) {
        console.error('Failed to load orders:', err);
        if (err.response?.data?.message) {
          setError(err.response.data.message);
        } else {
          setError('Failed to load orders. Please try again later.');
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchOrders();
  }, [id, isCurrentUser, currentPage, pageSize]);

  const handleNextPage = () => {
    setCurrentPage(prev => prev + 1);
  };

  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-6 text-white">Purchased Collections</h2>
          {isLoading ? (
            <div className="text-gray-400">Loading...</div>
          ) : error ? (
            <div className="text-red-400">{error}</div>
          ) : orders.length === 0 ? (
            <div className="glass-dark p-8 rounded-xl text-center text-gray-400">
              {isCurrentUser ? "You haven't purchased any collections yet" : "Only the current user can view their purchased collections."}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {orders.map((order) => (
                  <Link
                    key={order.collection?.id || order.id}
                    to={`/collection/${order.collection?.id || order.id}`}
                    className="group block card-glass rounded-xl overflow-hidden hover:-translate-y-2 transition-transform duration-300"
                  >
                    <div className="aspect-[4/3] bg-white/5 flex items-center justify-center">
                      {order.collection?.cover ? (
                        <img src={order.collection.cover} alt={order.collection.name} className="w-full h-full object-cover" />
                      ) : (
                        <span className="text-gray-400">No Cover</span>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-lg text-white line-clamp-1">{order.collection?.name || 'Unnamed Collection'}</h3>
                      <div className="mt-2 text-sm text-gray-400 line-clamp-2">{order.collection?.description}</div>
                    </div>
                  </Link>
                ))}
              </div>
              <div className="mt-8 flex justify-center gap-4">
                <button
                  onClick={handlePrevPage}
                  disabled={currentPage === 0}
                  className={`px-4 py-2 rounded-lg ${currentPage === 0 ? 'bg-gray-700 text-gray-500' : 'bg-blue-600 hover:bg-blue-700 text-white'}`}
                >
                  Previous
                </button>
                <span className="px-4 py-2 text-white">Page {currentPage + 1}</span>
                <button
                  onClick={handleNextPage}
                  className="px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Next
                </button>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default ProfileBuyPage; 