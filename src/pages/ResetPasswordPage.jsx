import { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useMutation } from 'react-query';
import { useEffect } from 'react';

import { resetPassword } from '@api/auth';
import { validatePassword } from '@utils/auth';

const ChangePasswordModal = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });
  
  const newPassword = watch('newPassword');
  
  const mutation = useMutation(resetPassword, {
    onSuccess: () => {
      toast.success('密码已成功更新');
      reset(); // 重置表单
      onClose(); // 关闭模态框
    },
    onError: (error) => {
      console.error('更新密码失败：', error);
      toast.error('更新密码失败，请确认旧密码是否正确');
    },
    onSettled: () => {
      setIsLoading(false);
    }
  });
  
  const onSubmit = (data) => {
    if (data.newPassword !== data.confirmPassword) {
      return;
    }
    
    setIsLoading(true);
    
    mutation.mutate({
      password_old: data.oldPassword,
      password_new: data.newPassword,
    });
  };
  
  useEffect(() => {
    if (isOpen) {
      reset();
    }
  }, [isOpen, reset]);
  
  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      className="fixed inset-0 z-50 overflow-y-auto"
    >
      <div className="flex min-h-screen items-center justify-center p-4">
        <Dialog.Overlay className="fixed inset-0 bg-black/70" />

        <div className="glass-dark relative rounded-xl p-6 w-full max-w-md">
          <Dialog.Title className="text-xl font-bold mb-6">
            修改密码
          </Dialog.Title>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <label htmlFor="oldPassword" className="block text-sm font-medium text-gray-300 mb-2">
                当前密码
              </label>
              <input
                id="oldPassword"
                type="password"
                className="glass w-full px-4 py-2 rounded-lg focus:ring-2 focus:ring-primary-500 focus:outline-none"
                {...register('oldPassword', {
                  required: '请输入当前密码',
                })}
              />
              {errors.oldPassword && (
                <p className="mt-1 text-sm text-red-500">{errors.oldPassword.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-300 mb-2">
                新密码
              </label>
              <input
                id="newPassword"
                type="password"
                className="glass w-full px-4 py-2 rounded-lg focus:ring-2 focus:ring-primary-500 focus:outline-none"
                {...register('newPassword', {
                  required: '请输入新密码',
                  validate: (value) =>
                    validatePassword(value) ||
                    '密码必须为 6-16 位字母、数字或下划线',
                })}
              />
              {errors.newPassword && (
                <p className="mt-1 text-sm text-red-500">{errors.newPassword.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-2">
                确认新密码
              </label>
              <input
                id="confirmPassword"
                type="password"
                className="glass w-full px-4 py-2 rounded-lg focus:ring-2 focus:ring-primary-500 focus:outline-none"
                {...register('confirmPassword', {
                  required: '请确认新密码',
                  validate: (value) => value === newPassword || '两次输入的密码不匹配',
                })}
              />
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-500">{errors.confirmPassword.message}</p>
              )}
            </div>

            <div className="flex justify-end pt-4 space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="btn-glass"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary"
              >
                {isLoading ? '更新中...' : '更新密码'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Dialog>
  );
};

export default ChangePasswordModal;