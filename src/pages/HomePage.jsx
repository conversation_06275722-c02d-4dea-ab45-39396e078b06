import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

import ResourceGrid from '@components/resources/ResourceGrid';
import useResources from '@hooks/useResources';
import useAuth from '@hooks/useAuth';

const HomePage = () => {
  const { refetchFeatured } = useResources();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    refetchFeatured();
  }, [refetchFeatured]);

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-radial from-primary-900/20 to-transparent"></div>
        <div className="absolute top-20 left-1/2 w-96 h-96 bg-primary-900/30 rounded-full blur-3xl -translate-x-1/2 -z-10"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              <span className="bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                Trade Digital Resources
              </span>{' '}
              <br className="hidden md:block" />
              with Ease
            </h1>
            <p className="text-lg md:text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Upload, buy, and sell digital assets in a secure marketplace. From images and videos to templates and more.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link to="/" className="btn-primary text-lg px-8 py-3">
                Browse Resources
              </Link>
              <Link to="/upload" className="btn-glass text-lg px-8 py-3">
                Create Collection
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">How It Works</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Our platform makes it easy to buy and sell digital resources in just a few steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="glass-dark p-6 rounded-xl text-center">
              <div className="w-16 h-16 bg-primary-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-8 h-8 text-primary-400"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Upload Your Resources</h3>
              <p className="text-gray-400">
                Create an account and upload your digital resources to start selling. Set your own prices and descriptions.
              </p>
            </div>

            <div className="glass-dark p-6 rounded-xl text-center">
              <div className="w-16 h-16 bg-primary-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-8 h-8 text-primary-400"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Discover Resources</h3>
              <p className="text-gray-400">
                Browse through our marketplace to find the perfect digital resources for your projects.
              </p>
            </div>

            <div className="glass-dark p-6 rounded-xl text-center">
              <div className="w-16 h-16 bg-primary-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-8 h-8 text-primary-400"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Buy and Sell</h3>
              <p className="text-gray-400">
                Purchase resources you need or sell your own creations. All transactions are secure and straightforward.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      {!isAuthenticated && (
        <section className="py-16 md:py-24">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="glass-dark overflow-hidden rounded-2xl border border-white/10 shadow-glass-strong relative">
              <div className="absolute inset-0 bg-gradient-to-br from-primary-800/30 via-transparent to-secondary-800/30"></div>
              <div className="relative p-8 md:p-12 lg:p-16">
                <div className="max-w-xl">
                  <h2 className="text-3xl font-bold text-white mb-4">
                    Ready to Start Trading?
                  </h2>
                  <p className="text-lg text-gray-300 mb-8">
                    Join our community of creators and buyers. Upload your first resource or explore our marketplace.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link to="/register" className="btn-primary text-lg px-8 py-3">
                      Create Account
                    </Link>
                    <Link to="/" className="btn-glass text-lg px-8 py-3">
                      Browse Resources
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

export default HomePage;