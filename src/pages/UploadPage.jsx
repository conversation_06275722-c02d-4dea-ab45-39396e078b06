import { motion } from 'framer-motion';
import UploadForm from '@components/resources/UploadForm';

const UploadPage = () => {
  return (
    <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">Create Collection</h1>
          <p className="text-gray-400">
            Create a collection of your digital assets. Upload multiple images and one video, set your price, and start earning.
          </p>
        </div>
        
        <div className="glass-dark p-6 rounded-xl shadow-glass-strong">
          <UploadForm />
        </div>
      </motion.div>
    </div>
  );
};

export default UploadPage;