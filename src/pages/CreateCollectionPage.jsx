import { Navigate } from 'react-router-dom';
import CreateCollectionForm from '@components/collections/CreateCollectionForm';
import useAuthStore from '@store/authStore';

const CreateCollectionPage = () => {
  const { isAuthenticated, user } = useAuthStore();

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-3xl">
      <div className="bg-gray-900 rounded-lg shadow-lg p-6 md:p-8">
        <h1 className="text-3xl font-bold text-white mb-6">Create Collection</h1>
        <p className="text-gray-400 mb-8">
          Create a collection of resources to sell or share. You can add multiple files to your collection.
        </p>

        <CreateCollectionForm />
      </div>
    </div>
  );
};

export default CreateCollectionPage; 