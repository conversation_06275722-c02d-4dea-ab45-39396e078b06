import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';

import ProfileHeader from '@components/profile/ProfileHeader';
import useResources from '@hooks/useResources';
import useAuth from '@hooks/useAuth';

const ProfilePage = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const { refetchUserSelling, refetchUserPurchased } = useResources();

  const isCurrentUser = !id || id === user?.id;

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (isCurrentUser) {
          await refetchUserSelling();
          await refetchUserPurchased();
        } else {
          // Fetch only selling resources for other users
          await refetchUserSelling();
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.error('Failed to load profile data');
      }
    };

    fetchData();
  }, [id, isCurrentUser, refetchUserSelling, refetchUserPurchased]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <ProfileHeader />
      </motion.div>
    </div>
  );
};

export default ProfilePage;