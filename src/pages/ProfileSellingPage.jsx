import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import useAuth from '@hooks/useAuth';
import { listCollections } from '@api/snaplink';

// If not already present, implement getMyCollections
import apiClient from '@api/client';
const getMyCollections = async () => {
  const { data } = await apiClient.post('/sl/v1/my/collections');
  return data;
};

const ProfileSellingPage = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const isCurrentUser = !id || id === user?.id;
  const [collections, setCollections] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCollections = async () => {
      setIsLoading(true);
      setError(null);
      try {
        let data;
        if (isCurrentUser) {
          data = await getMyCollections();
        } else {
          // For other users, fallback to listCollections with userId
          data = await listCollections({ userId: id });
        }
        setCollections(data.collections || []);
      } catch (err) {
        setError('Failed to load collections');
      } finally {
        setIsLoading(false);
      }
    };
    fetchCollections();
  }, [id, isCurrentUser]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-6 text-white">My Collections</h2>
          {isLoading ? (
            <div className="text-gray-400">Loading...</div>
          ) : error ? (
            <div className="text-red-400">{error}</div>
          ) : collections.length === 0 ? (
            <div className="glass-dark p-8 rounded-xl text-center text-gray-400">
              {isCurrentUser ? "You haven't created any collections yet" : "This user hasn't created any collections yet"}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {collections.map((collection) => (
                <Link
                  key={collection.id}
                  to={`/collection/${collection.id}`}
                  className="group block card-glass rounded-xl overflow-hidden hover:-translate-y-2 transition-transform duration-300"
                >
                  <div className="aspect-[4/3] bg-white/5 flex items-center justify-center">
                    {collection.cover ? (
                      <img src={collection.cover} alt={collection.name} className="w-full h-full object-cover" />
                    ) : (
                      <span className="text-gray-400">No Cover</span>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-lg text-white line-clamp-1">{collection.name}</h3>
                    <div className="mt-2 text-sm text-gray-400 line-clamp-2">{collection.description}</div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default ProfileSellingPage; 