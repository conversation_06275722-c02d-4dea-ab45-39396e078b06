import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import toast from 'react-hot-toast';

import { formatCurrency, formatRelativeTime, formatDateTime } from '@utils/format';
import useResources from '@hooks/useResources';
import useAuth from '@hooks/useAuth';

const ResourceDetailPage = () => {
  const { id } = useParams();
  const { purchaseResource, isLoading } = useResources();
  const { isAuthenticated, user } = useAuth();
  const [resource, setResource] = useState(null);
  const [isPurchasing, setIsPurchasing] = useState(false);

  useEffect(() => {
    const fetchResource = async () => {
      try {
        const resourceData = await getCollection({ resourceId: id });
        setResource(resourceData);
      } catch (error) {
        console.error('Failed to fetch resource:', error);
      }
    };

    if (id) {
      fetchResource();
    }
  }, [id, getCollection]);

  const handlePurchase = async () => {
    if (!isAuthenticated) {
      toast.error('Please log in to purchase this resource');
      return;
    }

    setIsPurchasing(true);
    try {
      await purchaseResource(id);
      setResource(prev => ({
        ...prev,
        isPurchased: true,
        purchasedAt: new Date().toISOString()
      }));
      toast.success('Purchase successful!');
    } catch (error) {
      toast.error('Failed to purchase resource');
      console.error('Purchase error:', error);
    } finally {
      setIsPurchasing(false);
    }
  };

  if (isLoading || !resource) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="glass-dark rounded-xl p-8 animate-pulse">
          <div className="flex flex-col lg:flex-row gap-8">
            <div className="lg:w-2/3">
              <div className="aspect-video bg-white/5 rounded-xl"></div>
            </div>
            <div className="lg:w-1/3 space-y-4">
              <div className="h-8 bg-white/5 rounded w-3/4"></div>
              <div className="h-6 bg-white/5 rounded w-1/2"></div>
              <div className="space-y-2">
                <div className="h-4 bg-white/5 rounded"></div>
                <div className="h-4 bg-white/5 rounded"></div>
                <div className="h-4 bg-white/5 rounded w-5/6"></div>
              </div>
              <div className="h-10 bg-white/5 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const isOwner = user?.id === resource.creator?.id;
  const canDownload = isOwner || resource.isPurchased;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="glass-dark rounded-xl overflow-hidden">
        <div className="flex flex-col lg:flex-row">
          <div className="lg:w-2/3 p-6">
            <div className="aspect-video bg-dark-300 rounded-xl overflow-hidden">
              <img
                src={resource.thumbnail || `https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80`}
                alt={resource.title}
                className="w-full h-full object-cover"
              />
            </div>

            <div className="mt-8">
              <h2 className="text-2xl font-bold mb-4">About this resource</h2>
              <p className="text-gray-300 whitespace-pre-line">{resource.description}</p>
            </div>

            {resource.isPurchased && (
              <div className="mt-8">
                <h2 className="text-2xl font-bold mb-4">Downloads</h2>
                <div className="glass-dark p-4 rounded-xl">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-primary-600/20 rounded">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6 text-primary-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                          />
                        </svg>
                      </div>
                      <div>
                        <span className="block text-white font-medium">Resource Files</span>
                        <span className="text-sm text-gray-400">
                          {resource.fileCount || 1} files | Purchased on {formatDateTime(resource.purchasedAt)}
                        </span>
                      </div>
                    </div>
                    <a
                      href="#"
                      className="btn-primary text-sm py-1 px-3"
                      onClick={(e) => {
                        e.preventDefault();
                        toast.success('Download started!');
                      }}
                    >
                      Download
                    </a>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="lg:w-1/3 p-6 lg:border-l border-white/10 space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-white">{resource.title}</h1>

              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-400">Posted {formatRelativeTime(resource.createdAt)}</span>
                <span className="mx-2 text-gray-600">•</span>
                <span className="text-sm bg-primary-500/20 text-primary-300 px-2 py-0.5 rounded-full capitalize">
                  {resource.category}
                </span>
              </div>

              <div className="mt-4 flex items-center">
                <Link to={`/profile/${resource.creator?.id}`} className="flex items-center">
                  {resource.creator?.avatar ? (
                    <img
                      src={resource.creator.avatar}
                      alt={resource.creator.username}
                      className="w-10 h-10 rounded-full"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-full bg-primary-600 flex items-center justify-center text-white text-lg font-bold">
                      {resource.creator?.username?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                  )}
                  <div className="ml-3">
                    <p className="text-sm font-medium text-white">{resource.creator?.username}</p>
                    <p className="text-xs text-gray-400">View profile</p>
                  </div>
                </Link>
              </div>
            </div>

            <div className="glass-dark-strong p-4 rounded-xl">
              <div className="text-center p-3">
                <div className="text-3xl font-bold text-white">{formatCurrency(resource.price)}</div>
                {resource.isPurchased ? (
                  <span className="text-green-400 mt-2 inline-block">✓ Already purchased</span>
                ) : isOwner ? (
                  <span className="text-primary-400 mt-2 inline-block">You own this resource</span>
                ) : (
                  <button
                    onClick={handlePurchase}
                    disabled={isPurchasing}
                    className="btn-primary w-full mt-4 flex items-center justify-center"
                  >
                    {isPurchasing ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      'Purchase Now'
                    )}
                  </button>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3">Resource Information</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">File type</span>
                  <span className="text-white">{resource.fileType || 'Multiple formats'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">File size</span>
                  <span className="text-white">{resource.fileSize || 'Varies'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Date created</span>
                  <span className="text-white">{formatDateTime(resource.createdAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Last updated</span>
                  <span className="text-white">{formatDateTime(resource.updatedAt || resource.createdAt)}</span>
                </div>
              </div>
            </div>

            {!canDownload && (
              <div className="glass-dark p-4 rounded-xl">
                <div className="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-primary-400 mr-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm text-gray-300">
                    Purchase this resource to download the files
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourceDetailPage;