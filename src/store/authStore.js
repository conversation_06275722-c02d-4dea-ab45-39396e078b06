import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { getStoragePrefix } from '@utils/config';
import { setTokens as setTokensInStorage, clearTokens as clearTokensFromStorage } from '@utils/auth';

const useAuthStore = create(
  persist(
    (set) => ({
      user: null,
      token: null, 
      isAuthenticated: false, 
      isLoading: false,
      error: null,
      
      setToken: (token) => {
        setTokensInStorage(token); 
        set({ token, isAuthenticated: !!token, isLoading: false, error: null });
      },
      
      setUser: (user) => set({ user }), 
      
      logout: () => {
        clearTokensFromStorage();
        set({ user: null, token: null, isAuthenticated: false, isLoading: false, error: null });
      },
      
      updateUser: (userData) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...userData } : userData,
        }));
      },
      
      setError: (error) => set({ error, isLoading: false }),
      clearError: () => set({ error: null }),
      
      setLoading: (isLoading) => set({ isLoading }),
    }),
    {
      name: `${getStoragePrefix()}auth`,
      partialize: (state) => ({ 
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);

export default useAuthStore;