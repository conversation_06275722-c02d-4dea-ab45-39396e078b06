import { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';

import Layout from '@components/layout/Layout';
import GlassLoader from '@components/ui/GlassLoader';
import ErrorFallback from '@components/ui/ErrorFallback';
import ProtectedRoute from '@components/auth/ProtectedRoute';

// Lazy-loaded pages
const HomePage = lazy(() => import('@pages/HomePage'));
const ResourceDetailPage = lazy(() => import('@pages/ResourceDetailPage'));
const ProfilePage = lazy(() => import('@pages/ProfilePage'));
const UploadPage = lazy(() => import('@pages/UploadPage'));
const TransactionsPage = lazy(() => import('@pages/TransactionsPage'));
const LoginPage = lazy(() => import('@pages/LoginPage'));
const RegisterPage = lazy(() => import('@pages/RegisterPage'));
const ForgotPasswordPage = lazy(() => import('@pages/ForgotPasswordPage'));
const ResetPasswordPage = lazy(() => import('@pages/ResetPasswordPage'));
const NotFoundPage = lazy(() => import('@pages/NotFoundPage'));
const ProfileSellingPage = lazy(() => import('@pages/ProfileSellingPage'));
const ProfileBuyPage = lazy(() => import('@pages/ProfileBuyPage'));
const CreateCollectionPage = lazy(() => import('@pages/CreateCollectionPage'));

function App() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Suspense fallback={<GlassLoader />}>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />

            <Route path="resource/:id" element={<ResourceDetailPage />} />

            {/* Protected routes */}
            <Route element={<ProtectedRoute />}>
              <Route path="upload" element={<UploadPage />} />
              <Route path="collection/create" element={<CreateCollectionPage />} />
              <Route path="profile/:id?" element={<ProfilePage />} />
              <Route path="profile/:id?/selling" element={<ProfileSellingPage />} />
              <Route path="profile/:id?/buy" element={<ProfileBuyPage />} />
              <Route path="transactions" element={<TransactionsPage />} />
            </Route>

            {/* Auth routes */}
            <Route path="login" element={<LoginPage />} />
            <Route path="register" element={<RegisterPage />} />
            <Route path="forgot-password" element={<ForgotPasswordPage />} />
            <Route path="reset-password" element={<ResetPasswordPage />} />

            {/* 404 */}
            <Route path="*" element={<NotFoundPage />} />
          </Route>
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
}

export default App;