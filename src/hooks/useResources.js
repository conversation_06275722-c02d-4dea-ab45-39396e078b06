import { useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import {
  getUploadResourceURL,
  buyCollection,
  getMyOrders,
  getUserResources
} from '@api/snaplink';
import useResourceStore from '@store/resourceStore';
import useAuthStore from '@store/authStore';

export default function useResources() {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const {
    featuredResources,
    userSelling,
    userPurchased,
    selectedResource,
    setFeaturedResources,
    setSelectedResource,
    setUserSelling,
    setUserPurchased,
    purchaseResource: storePurchaseResource,
    addResource: storeAddResource,
    setLoading,
    setError,
    error,
  } = useResourceStore();

  // Get featured resources
  const refetchFeatured = () => { };

  // Get user selling resources
  const { refetch: refetchUserSelling } = useQuery(
    ['userSellingResources', user?.id],
    () => getUserResources({ page: 0, size: 10 }),
    {
      enabled: !!user?.id,
      onSuccess: (data) => {
        setUserSelling(data.collections || []);
      },
      onError: (error) => {
        console.error('Error fetching user selling resources:', error);
        toast.error('Failed to load your collections');
      }
    }
  );

  // Get user purchased resources
  const { refetch: refetchUserPurchased } = useQuery(
    ['userPurchasedResources', user?.id],
    () => getMyOrders({ page: 0, size: 10 }),
    {
      enabled: !!user?.id,
      onSuccess: (data) => {
        setUserPurchased(data.resources || []);
      },
      onError: (error) => {
        console.error('Error fetching user purchased resources:', error);
        toast.error('Failed to load your purchases');
      }
    }
  );

  // Upload resource mutation
  const uploadMutation = useMutation(getUploadResourceURL, {
    onSuccess: (data) => {
      storeAddResource(data.resource);

      // Invalidate and refetch selling resources
      queryClient.invalidateQueries(['userSellingResources', user?.id]);

      toast.success('Resource uploaded successfully!');
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to upload resource');
    },
  });

  // Purchase resource mutation
  const purchaseMutation = useMutation(
    (collectionId) => buyCollection({ collectionId }),
    {
      onSuccess: (data) => {
        storePurchaseResource(data.resource);

        // Invalidate relevant queries
        queryClient.invalidateQueries(['userPurchasedResources', user?.id]);

        toast.success('Resource purchased successfully!');
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to purchase resource');
      },
    }
  );

  // Upload handler
  const handleUpload = useCallback((resourceData) => {
    return uploadMutation.mutateAsync(resourceData);
  }, [uploadMutation]);

  // Purchase handler
  const handlePurchase = useCallback((resourceId) => {
    return purchaseMutation.mutateAsync(resourceId);
  }, [purchaseMutation]);

  return {
    featuredResources,
    userSelling,
    userPurchased,
    selectedResource,
    isLoading: uploadMutation.isLoading || purchaseMutation.isLoading,
    error,
    uploadResource: handleUpload,
    purchaseResource: handlePurchase,
    refetchFeatured,
    refetchUserPurchased,
    refetchUserSelling,
  };
}