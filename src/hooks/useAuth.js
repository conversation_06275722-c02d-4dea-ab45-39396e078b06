import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { 
  login as loginApi, 
  register as registerApi, 
  getCurrentUser, 
  logout as logoutApi 
} from '@api/auth';
import useAuthStore from '@store/authStore';

export default function useAuth() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const user = useAuthStore((state) => state.user);
  const token = useAuthStore((state) => state.token);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isLoadingStore = useAuthStore((state) => state.isLoading);
  const errorStore = useAuthStore((state) => state.error);

  const { setToken, setUser, logout: storeLogout, setError, clearError, setLoading } = useAuthStore.getState();
  
  const { refetch: refetchUser, isLoading: isLoadingUser } = useQuery('currentUser', getCurrentUser, {
    enabled: !!token,
    staleTime: Infinity,
    cacheTime: Infinity,
    onSuccess: (response) => {
      if (response && response.data) {
        setUser(response.data);
      }
      setLoading(false);
    },
    onError: (err) => {
      console.error('Failed to fetch current user:', err);
      if (err?.response?.status === 401) {
        storeLogout();
      } else {
        setError(err.message || 'Failed to fetch current user');
      }
      setLoading(false);
    },
  });
  
  const loginMutation = useMutation(loginApi, {
    onSuccess: (apiResponse) => {
      // setLoading(true); // Removed, setLoading(true) is called in handleLogin
      if (apiResponse && apiResponse.data && apiResponse.data.access_token) {
        setToken(apiResponse.data.access_token);
        queryClient.invalidateQueries('currentUser'); // Invalidate to refetch user data
      } else {
        console.error('Login success response missing access_token:', apiResponse);
        setError('Login failed: Invalid response from server.');
        toast.error('Login failed: Invalid response from server.');
        // Do not navigate or toast success if token is missing
        return; // Exit early
      }
      toast.success('Login successful!');
      navigate('/');
    },
    onError: (err) => {
      const message = err.response?.data?.message || 'Failed to login';
      setError(message);
      toast.error(message);
      // setLoading(false); // Moved to onSettled
    },
    onSettled: () => {
      setLoading(false); // Centralized store's loading=false state update
    }
  });
  
  const registerMutation = useMutation(registerApi, {
    onSuccess: (apiResponse) => {
      // setLoading(true); // Removed, setLoading(true) is called in handleRegister
      if (apiResponse && apiResponse.data && apiResponse.data.access_token) {
        setToken(apiResponse.data.access_token);
        queryClient.invalidateQueries('currentUser'); // Invalidate to refetch user data
      } else {
        console.error('Registration success response missing access_token:', apiResponse);
        setError('Registration failed: Invalid response from server.');
        toast.error('Registration failed: Invalid response from server.');
        // Do not navigate or toast success if token is missing
        return; // Exit early
      }
      toast.success('Registration successful!');
      navigate('/');
    },
    onError: (err) => {
      const message = err.response?.data?.message || 'Failed to register';
      setError(message);
      toast.error(message);
      // setLoading(false); // Moved to onSettled
    },
    onSettled: () => {
      setLoading(false); // Centralized store's loading=false state update
    }
  });
  
  const logoutMutation = useMutation(logoutApi, {
    onSuccess: () => {
    },
    onError: (err) => {
      console.error('API logout failed:', err);
      toast.error('Logout failed on server, clearing local session.');
    },
    onSettled: () => {
      storeLogout();
      queryClient.removeQueries('currentUser');
      toast.success('Logged out successfully');
      navigate('/login');
    },
  });
  
  const handleLogin = useCallback((credentials) => {
    clearError();
    setLoading(true);
    return loginMutation.mutateAsync(credentials);
  }, [loginMutation, clearError, setLoading]);
  
  const handleRegister = useCallback((userData) => {
    clearError();
    setLoading(true);
    return registerMutation.mutateAsync(userData);
  }, [registerMutation, clearError, setLoading]);
  
  const handleLogout = useCallback(() => {
    logoutMutation.mutate();
  }, [logoutMutation]);
  
  return {
    user,
    isAuthenticated,
    isLoading: isLoadingStore || loginMutation.isLoading || registerMutation.isLoading || logoutMutation.isLoading || isLoadingUser,
    error: errorStore,
    login: handleLogin,
    register: handleRegister,
    logout: handleLogout,
    refetchUser,
  };
}