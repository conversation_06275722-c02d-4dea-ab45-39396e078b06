import { useCallback, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import {
  createCollection,
  initiateMultipartUpload,
  getUploadPartUrl,
  completeMultipartUpload,
  addCollectionResource,
  publishCollection,
  getUploadResourceURL,
} from '@api/snaplink';

import { multipartUpload, fileToBase64, directUpload, isImageFile, isVideoFile } from '@utils/uploadHelpers';

const useCollections = () => {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(0);

  // Create collection mutation
  const createCollectionMutation = useMutation(createCollection, {
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to create collection');
    },
  });

  // Initiate multipart upload mutation
  const initiateUploadMutation = useMutation(initiateMultipartUpload, {
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to initiate upload');
    },
  });

  // Get upload part URL mutation
  const getUploadPartUrlMutation = useMutation(getUploadPartUrl, {
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to get upload URL');
    },
  });

  // Add collection resource mutation
  const addResourceMutation = useMutation(addCollectionResource, {
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to add resource to collection');
    },
  });

  // Complete multipart upload mutation
  const completeUploadMutation = useMutation(completeMultipartUpload, {
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to complete upload');
    },
  });

  // Get upload resource URL mutation (for direct upload)
  const getUploadResourceURLMutation = useMutation(getUploadResourceURL, {
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to get upload URL');
    },
  });

  // Publish collection mutation
  const publishCollectionMutation = useMutation(publishCollection, {
    onSuccess: () => {
      // Invalidate collections queries to refresh the data
      queryClient.invalidateQueries('collections');
      queryClient.invalidateQueries('userCollections');
      toast.success('Collection published successfully!');
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to publish collection');
    },
  });

  /**
   * Upload a file using the appropriate method (direct for images, multipart for videos)
   * @param {File} file - The file to upload
   * @param {string} collectionId - The collection ID
   * @param {Object} metadata - Additional metadata for the file
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} - The resource details
   */
  const uploadFileToCollection = useCallback(async (file, collectionId, metadata = {}, onProgress = () => {}) => {
    try {
      let resource;

      if (isImageFile(file)) {
        // Use direct upload for images
        resource = await directUpload({
          file,
          getUploadUrl: (data) => getUploadResourceURLMutation.mutateAsync(data),
          onProgress,
        });
      } else if (isVideoFile(file)) {
        // Use multipart upload for videos
        resource = await multipartUpload({
          file,
          initiateUpload: (data) => initiateUploadMutation.mutateAsync(data),
          getPartUrl: (data) => getUploadPartUrlMutation.mutateAsync(data),
          completeUpload: (data) => completeUploadMutation.mutateAsync(data),
          onProgress,
          metadata,
        });
      } else {
        throw new Error(`Unsupported file type: ${file.type}`);
      }

      // Add resource to collection
      await addResourceMutation.mutateAsync({
        collectionId,
        resourceId: resource.id,
      });

      return resource;
    } catch (error) {
      console.error('Error uploading file to collection:', error);
      throw error;
    }
  }, [
    initiateUploadMutation,
    getUploadPartUrlMutation,
    completeUploadMutation,
    getUploadResourceURLMutation,
    addResourceMutation
  ]);

  /**
   * Create a collection with files
   * @param {Object} collectionData - Collection metadata (title, desc, banner, amount)
   * @param {Array<File>} files - Array of files to upload
   * @returns {Promise<Object>} - The created collection
   */
  const createCollectionWithFiles = useCallback(async (collectionData, files) => {
    setIsLoading(true);
    setCurrentProgress(0);

    try {
      // Step 1: Prepare collection data for API
      let bannerBase64 = '';
      if (collectionData.banner) {
        bannerBase64 = await fileToBase64(collectionData.banner);
      }

      const apiCollectionData = {
        title: collectionData.title,
        desc: collectionData.description || '',
        banner: bannerBase64,
        amount: collectionData.amount.toString(),
      };

      // Step 2: Create the collection
      const createResponse = await createCollectionMutation.mutateAsync(apiCollectionData);
      const collectionId = createResponse.collection_id || createResponse.data?.collection_id || createResponse.data?.id;

      if (!collectionId) {
        throw new Error('Collection ID not returned from API');
      }

      // Step 3-6: Upload each file to the collection
      const totalFiles = files.length;
      const resources = [];

      for (let i = 0; i < totalFiles; i++) {
        const file = files[i];
        const fileType = isImageFile(file) ? 'image' : isVideoFile(file) ? 'video' : 'unknown';

        const resource = await uploadFileToCollection(
          file,
          collectionId,
          {
            name: file.name,
            description: `File ${i + 1} for collection ${collectionData.title}`,
          },
          (fileProgress) => {
            // Calculate overall progress: each file gets equal weight
            const baseProgress = (i / totalFiles) * 90;
            const currentFileProgress = (fileProgress / 100) * (90 / totalFiles);
            setCurrentProgress(baseProgress + currentFileProgress);
          }
        );

        resources.push(resource);
        console.log(`Uploaded ${fileType} file: ${file.name}`);
      }

      // Step 7: Publish the collection
      await publishCollectionMutation.mutateAsync(collectionId);

      setCurrentProgress(100);
      toast.success('Collection created and published successfully!');

      return { collectionId, resources };
    } catch (error) {
      console.error('Error creating collection with files:', error);
      toast.error('Failed to create collection. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [
    createCollectionMutation,
    uploadFileToCollection,
    publishCollectionMutation
  ]);

  return {
    isLoading,
    currentProgress,
    createCollectionWithFiles,
    uploadFileToCollection,
    publishCollection: publishCollectionMutation.mutateAsync,
  };
};

export default useCollections; 