import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';

import useAuth from '@hooks/useAuth';
import { sendEmail as sendVerificationEmailApi } from '@api/auth'; 
import { validatePassword } from '@utils/auth';
import { getErrorMessage } from '@utils/errorMessages';

const RegisterForm = () => {
  const { register: registerUser, isLoading: isRegistering } = useAuth(); 
  const navigate = useNavigate();
  const [registerError, setRegisterError] = useState('');
  const [isCodeSending, setIsCodeSending] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [codeSendError, setCodeSendError] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    getValues, 
    formState: { errors, isValid: isFormValid }, 
  } = useForm({
    mode: 'onTouched', 
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      main_code: '', 
    },
  });

  const password = watch('password');
  const emailValue = watch('email'); 

  const handleSendVerificationCode = async () => {
    setCodeSendError('');
    setIsCodeSending(true);
    try {
      const email = getValues('email');
      if (!email || errors.email) { 
        setCodeSendError('Please enter a valid email address.');
        setIsCodeSending(false);
        return;
      }
      await sendVerificationEmailApi({ 
        email, 
        tenant_id: 'SnapLink', 
        type: 'Register' 
      });
      setCodeSent(true);
    } catch (error) {
      setCodeSendError(getErrorMessage(error));
    } finally {
      setIsCodeSending(false);
    }
  };

  const onSubmit = async (data) => {
    if (data.password !== data.confirmPassword) {
      setRegisterError('Passwords do not match.');
      return;
    }

    if (!codeSent) {
      setRegisterError('Please send and enter the verification code first.');
      return;
    }

    try {
      setRegisterError('');
      const payload = {
        email: data.email,
        password: data.password,
        mail_code: data.main_code,
        tenant_id: 'SnapLink',
      };
      await registerUser(payload); 
      navigate('/'); 
    } catch (error) {
      setRegisterError(getErrorMessage(error));
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {registerError && (
        <div className="glass-dark p-3 rounded-lg text-red-500 text-sm">
          {registerError}
        </div>
      )}
      {codeSendError && (
        <div className="glass-dark p-3 rounded-lg text-red-500 text-sm">
          {codeSendError}
        </div>
      )}

      <div>
        <label htmlFor="username" className="block text-sm font-medium text-gray-300">
          Username
        </label>
        <div className="mt-1">
          <input
            id="username"
            type="text"
            autoComplete="username"
            className="input-glass w-full"
            {...register('username', {
              required: 'Username is required',
              minLength: {
                value: 3,
                message: 'Username must be at least 3 characters',
              },
            })}
          />
          {errors.username && (
            <p className="mt-1 text-sm text-red-500">{errors.username.message}</p>
          )}
        </div>
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-300">
          Email
        </label>
        <div className="mt-1 flex items-center space-x-2">
          <input
            id="email"
            type="email"
            autoComplete="email"
            className="input-glass w-full"
            disabled={codeSent} 
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address',
              },
            })}
          />
          <button 
            type="button"
            onClick={handleSendVerificationCode}
            disabled={isCodeSending || codeSent || !emailValue || !!errors.email}
            className="btn-secondary px-3 py-2 text-sm whitespace-nowrap disabled:opacity-50"
          >
            {isCodeSending ? 'Sending...' : codeSent ? 'Code Sent' : 'Send Code'}
          </button>
        </div>
        {errors.email && (
          <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
        )}
      </div>

      {codeSent && (
        <div>
          <label htmlFor="main_code" className="block text-sm font-medium text-gray-300">
            Verification Code
          </label>
          <div className="mt-1">
            <input
              id="main_code"
              type="text"
              className="input-glass w-full"
              {...register('main_code', {
                required: 'Verification code is required',
                minLength: { value: 6, message: 'Code must be at least 6 characters' }, 
                maxLength: { value: 6, message: 'Code must be at most 6 characters' }, 
              })}
            />
            {errors.main_code && (
              <p className="mt-1 text-sm text-red-500">{errors.main_code.message}</p>
            )}
          </div>
        </div>
      )}

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-300">
          Password
        </label>
        <div className="mt-1">
          <input
            id="password"
            type="password"
            autoComplete="new-password"
            className="input-glass w-full"
            {...register('password', {
              required: 'Password is required',
              validate: (value) =>
                validatePassword(value) ||
                '密码必须为 6-16 位字母、数字或下划线',
            })}
          />
          {errors.password && (
            <p className="mt-1 text-sm text-red-500">{errors.password.message}</p>
          )}
        </div>
      </div>

      <div>
        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
          Confirm Password
        </label>
        <div className="mt-1">
          <input
            id="confirmPassword"
            type="password"
            className="input-glass w-full"
            {...register('confirmPassword', {
              required: 'Please confirm your password',
              validate: (value) => value === password || 'Passwords do not match',
            })}
          />
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-500">{errors.confirmPassword.message}</p>
          )}
        </div>
      </div>

      <div>
        <button
          type="submit"
          className="btn-primary w-full flex justify-center"
          disabled={isRegistering || isCodeSending || !codeSent} 
        >
          {isRegistering ? (
            <svg
              className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          ) : (
            'Register'
          )}
        </button>
      </div>

      <div className="text-sm text-center">
        <span className="text-gray-400">Already have an account? </span>
        <Link to="/login" className="text-primary-400 hover:text-primary-300">
          Sign in
        </Link>
      </div>
    </form>
  );
};

export default RegisterForm;