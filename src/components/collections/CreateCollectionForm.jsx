import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import { FiUpload, FiX, FiImage } from 'react-icons/fi';

import useCollections from '@hooks/useCollections';

const CreateCollectionForm = () => {
  const navigate = useNavigate();
  const { createCollectionWithFiles, isLoading, currentProgress } = useCollections();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    isPublic: true,
  });

  const [coverImage, setCoverImage] = useState(null);
  const [coverImagePreview, setCoverImagePreview] = useState('');
  const [files, setFiles] = useState([]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Handle cover image upload
  const { getRootProps: getCoverRootProps, getInputProps: getCoverInputProps } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      const file = acceptedFiles[0];
      setCoverImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = () => {
        setCoverImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    },
  });

  // Handle resource files upload
  const { getRootProps: getFilesRootProps, getInputProps: getFilesInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      setFiles([...files, ...acceptedFiles]);
    },
  });

  // Remove a file from the list
  const removeFile = (index) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Please enter a collection name');
      return;
    }

    if (!coverImage) {
      toast.error('Please upload a cover image');
      return;
    }

    if (files.length === 0) {
      toast.error('Please upload at least one file');
      return;
    }

    try {
      // Prepare collection data
      const collectionData = {
        ...formData,
        price: parseFloat(formData.price) || 0,
      };

      // Create collection with files
      const result = await createCollectionWithFiles(collectionData, [coverImage, ...files]);

      toast.success('Collection created successfully!');
      navigate(`/collection/${result.collectionId}`);
    } catch (error) {
      console.error('Error creating collection:', error);
      toast.error('Failed to create collection. Please try again.');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Collection Details */}
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-white">
            Collection Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md shadow-sm text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="Enter collection name"
            required
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-white">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md shadow-sm text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="Describe your collection"
          />
        </div>

        <div>
          <label htmlFor="price" className="block text-sm font-medium text-white">
            Price (USD)
          </label>
          <input
            type="number"
            id="price"
            name="price"
            value={formData.price}
            onChange={handleChange}
            min="0"
            step="0.01"
            className="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md shadow-sm text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="0.00"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isPublic"
            name="isPublic"
            checked={formData.isPublic}
            onChange={handleChange}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-700 rounded bg-gray-800"
          />
          <label htmlFor="isPublic" className="ml-2 block text-sm text-white">
            Make this collection public
          </label>
        </div>
      </div>

      {/* Cover Image Upload */}
      <div>
        <label className="block text-sm font-medium text-white mb-2">
          Cover Image
        </label>
        <div
          {...getCoverRootProps()}
          className={`border-2 border-dashed border-gray-700 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 transition-colors ${coverImagePreview ? 'border-primary-500' : ''
            }`}
        >
          <input {...getCoverInputProps()} />
          {coverImagePreview ? (
            <div className="relative w-full">
              <img
                src={coverImagePreview}
                alt="Cover preview"
                className="w-full h-48 object-cover rounded"
              />
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  setCoverImage(null);
                  setCoverImagePreview('');
                }}
                className="absolute top-2 right-2 bg-red-500 rounded-full p-1 text-white"
              >
                <FiX size={16} />
              </button>
            </div>
          ) : (
            <div className="text-center">
              <FiImage className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-400">
                Click or drag and drop to upload a cover image
              </p>
              <p className="text-xs text-gray-500 mt-1">
                JPG, PNG or GIF (max 5MB)
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Resource Files Upload */}
      <div>
        <label className="block text-sm font-medium text-white mb-2">
          Collection Files
        </label>
        <div
          {...getFilesRootProps()}
          className="border-2 border-dashed border-gray-700 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 transition-colors"
        >
          <input {...getFilesInputProps()} />
          <FiUpload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-400">
            Click or drag and drop to upload files
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Any file format (max 100MB per file)
          </p>
        </div>
      </div>

      {/* Files Preview */}
      {files.length > 0 && (
        <div className="mt-4">
          <h3 className="text-sm font-medium text-white mb-2">
            Selected Files ({files.length})
          </h3>
          <ul className="space-y-2">
            {files.map((file, index) => (
              <li
                key={`${file.name}-${index}`}
                className="flex items-center justify-between bg-gray-800 p-3 rounded-lg"
              >
                <div className="flex items-center">
                  <span className="text-sm text-white truncate max-w-xs">
                    {file.name}
                  </span>
                  <span className="text-xs text-gray-400 ml-2">
                    ({(file.size / 1024 / 1024).toFixed(2)} MB)
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-400"
                >
                  <FiX size={18} />
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Progress Bar */}
      {isLoading && (
        <div className="w-full bg-gray-800 rounded-full h-2.5">
          <div
            className="bg-primary-600 h-2.5 rounded-full"
            style={{ width: `${currentProgress}%` }}
          ></div>
          <p className="text-xs text-gray-400 mt-1 text-right">
            {currentProgress}%
          </p>
        </div>
      )}

      {/* Submit Button */}
      <div className="pt-4">
        <button
          type="submit"
          disabled={isLoading}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Creating Collection...' : 'Create Collection'}
        </button>
      </div>
    </form>
  );
};

export default CreateCollectionForm; 