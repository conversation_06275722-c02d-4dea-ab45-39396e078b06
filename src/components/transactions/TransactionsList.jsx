import { Link } from 'react-router-dom';
import { formatCurrency, formatDateTime } from '@utils/format';
import useTransactions from '@hooks/useTransactions';

const TransactionsList = () => {
  const { transactions, pagination, isLoading, changePage } = useTransactions();

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="glass-dark rounded-lg p-4 animate-pulse">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div className="flex-1">
                <div className="h-5 bg-white/5 rounded w-1/4 mb-2"></div>
                <div className="h-4 bg-white/5 rounded w-1/3"></div>
              </div>
              <div className="mt-2 md:mt-0">
                <div className="h-5 bg-white/5 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!transactions || transactions.length === 0) {
    return (
      <div className="glass-dark rounded-lg p-8 text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-12 h-12 text-gray-500 mx-auto mb-4"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"
          />
        </svg>
        <p className="text-gray-400">You don't have any transactions yet</p>
        <Link to="/" className="btn-primary mt-4 inline-block">
          Browse Resources
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="space-y-4">
        {transactions.map((transaction) => (
          <div key={transaction.id} className="glass-dark rounded-lg p-4 hover:bg-white/5 transition-colors">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 flex-shrink-0">
                  <img
                    src={transaction.resource?.thumbnail || `https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80`}
                    alt={transaction.resource?.title}
                    className="w-full h-full object-cover rounded-md"
                  />
                </div>
                <div>
                  <Link
                    to={`/resource/${transaction.resource?.id}`}
                    className="text-white font-medium hover:text-primary-400"
                  >
                    {transaction.resource?.title || 'Resource'}
                  </Link>
                  <div className="flex items-center mt-1 text-sm text-gray-400">
                    <span>
                      {transaction.type === 'purchase' ? 'Purchased from ' : 'Sold to '}
                    </span>
                    <Link
                      to={`/profile/${transaction.type === 'purchase' ? transaction.seller?.id : transaction.buyer?.id}`}
                      className="ml-1 text-primary-400 hover:underline"
                    >
                      {transaction.type === 'purchase' ? transaction.seller?.username : transaction.buyer?.username}
                    </Link>
                    <span className="mx-1">•</span>
                    <span>{formatDateTime(transaction.createdAt)}</span>
                  </div>
                </div>
              </div>
              <div className="mt-4 md:mt-0 flex items-center">
                <span className={`text-lg font-medium ${transaction.type === 'sale' ? 'text-green-400' : 'text-white'}`}>
                  {transaction.type === 'sale' ? '+' : ''}{formatCurrency(transaction.amount)}
                </span>
                <span className="ml-2 px-2 py-1 text-xs rounded-full bg-glass-dark border border-white/10">
                  {transaction.status}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {pagination.totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <nav className="glass-dark rounded-lg inline-flex">
            <button
              onClick={() => changePage(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="px-3 py-2 rounded-l-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/5"
            >
              Previous
            </button>

            <div className="flex items-center border-l border-r border-white/10 px-4">
              <span className="text-sm text-gray-400">
                Page {pagination.page} of {pagination.totalPages}
              </span>
            </div>

            <button
              onClick={() => changePage(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
              className="px-3 py-2 rounded-r-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/5"
            >
              Next
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default TransactionsList;