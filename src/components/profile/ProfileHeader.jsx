import { useState } from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { formatDate } from '@utils/format';
import { getMyProfile as getUserProfile } from '@api/users';
import useAuth from '@hooks/useAuth';
import EditProfileModal from './EditProfileModal';
import ChangePasswordModal from './ChangePasswordModal';

const ProfileHeader = () => {
  const { id } = useParams();
  const { user: currentUser } = useAuth();
  const [isCurrentUser, setIsCurrentUser] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] = useState(false);
  
  const { data, isLoading } = useQuery(
    ['userProfile', id || currentUser?.id],
    () => getUserProfile(id || currentUser?.id),
    {
      onSuccess: (data) => {
        setIsCurrentUser(!id || id === currentUser?.id);
      },
      enabled: !!id || !!currentUser?.id,
    }
  );
  
  const user = data?.user || currentUser;
  
  if (isLoading) {
    return (
      <div className="glass-dark rounded-xl p-8">
        <div className="flex flex-col md:flex-row items-center gap-6">
          <div className="w-24 h-24 rounded-full bg-white/5 animate-pulse"></div>
          <div className="space-y-3 flex-grow text-center md:text-left">
            <div className="h-7 bg-white/5 rounded w-1/3 animate-pulse mx-auto md:mx-0"></div>
            <div className="h-5 bg-white/5 rounded w-1/2 animate-pulse mx-auto md:mx-0"></div>
            <div className="flex flex-wrap justify-center md:justify-start gap-4 mt-4">
              <div className="h-5 bg-white/5 rounded w-24 animate-pulse"></div>
              <div className="h-5 bg-white/5 rounded w-24 animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="glass-dark rounded-xl p-8">
      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="relative">
          {user?.avatar ? (
            <img
              src={user.avatar}
              alt={user.username}
              className="w-24 h-24 rounded-full object-cover border-2 border-primary-500"
            />
          ) : (
            <div className="w-24 h-24 rounded-full bg-gradient-to-r from-primary-600 to-secondary-600 flex items-center justify-center text-white text-3xl font-bold">
              {user?.username?.charAt(0)?.toUpperCase() || 'U'}
            </div>
          )}
          
          {isCurrentUser && (
            <button
              type="button"
              onClick={() => setIsEditModalOpen(true)}
              className="absolute bottom-0 right-0 bg-primary-600 p-2 rounded-full hover:bg-primary-500 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                />
              </svg>
            </button>
          )}
        </div>
        
        <div className="space-y-2 flex-grow text-center md:text-left">
          <h1 className="text-2xl font-bold text-white">{user?.username || 'Username'}</h1>
          <p className="text-gray-400">
            Member since {formatDate(user?.createdAt || new Date())}
          </p>
          <div className="flex flex-wrap justify-center md:justify-start gap-4 mt-4">
            <div className="glass p-2 px-3 rounded-lg text-sm">
              <span className="text-gray-400">Resources:</span>{' '}
              <span className="font-medium">{user?.resourceCount || 0}</span>
            </div>
            <div className="glass p-2 px-3 rounded-lg text-sm">
              <span className="text-gray-400">Sales:</span>{' '}
              <span className="font-medium">{user?.salesCount || 0}</span>
            </div>
          </div>
        </div>
        
        {/* Add the button here, potentially wrapped in a div for alignment */}
        {isCurrentUser && (
          <div className="mt-4 md:mt-0 flex items-center">
            <button
              onClick={() => setIsChangePasswordModalOpen(true)}
              className="glass p-2 px-3 rounded-lg text-sm hover:bg-primary-600/20 transition-colors"
            >
              <span className="text-primary-400">修改密码</span>
            </button>
          </div>
        )}

        {!isCurrentUser && (
          <div className="mt-4 md:mt-0">
            <button className="btn-primary">
              Follow
            </button>
          </div>
        )}
      </div>
      
      {user?.bio && (
        <div className="mt-6 pt-6 border-t border-white/10">
          <p className="text-gray-300">{user.bio}</p>
        </div>
      )}

      {isCurrentUser && (
        <EditProfileModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          user={user}
        />
      )}
      
      <ChangePasswordModal
        isOpen={isChangePasswordModalOpen}
        onClose={() => setIsChangePasswordModalOpen(false)}
      />
    </div>
  );
};

export default ProfileHeader;