import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDropzone } from 'react-dropzone';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';

import { ALL_ACCEPTED_FILE_TYPES } from '@utils/config';
import { formatFileSize } from '@utils/format';
import { detectFileType, separateFilesByType } from '@utils/uploadHelpers';
import useCollections from '@hooks/useCollections';

const UploadForm = () => {
  const { createCollectionWithFiles, isLoading, currentProgress } = useCollections();
  const [bannerPreview, setBannerPreview] = useState(null);
  const [files, setFiles] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm({
    defaultValues: {
      title: '',
      description: '',
      price: '',
      banner: null,
      files: null,
    },
  });
  
  // Banner image dropzone
  const {
    getRootProps: getBannerRootProps,
    getInputProps: getBannerInputProps,
  } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    },
    maxSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        setBannerPreview(URL.createObjectURL(acceptedFiles[0]));
        setValue('banner', acceptedFiles[0]);
      }
    },
    onDropRejected: (rejectedFiles) => {
      if (rejectedFiles[0]?.errors[0]?.code === 'file-too-large') {
        toast.error('Banner image is too large. Maximum size is 5MB.');
      } else {
        toast.error('Please upload a valid image file (JPEG, PNG, GIF, WEBP).');
      }
    },
  });
  
  // Files dropzone with auto-detection and multiple image support
  const {
    getRootProps: getFilesRootProps,
    getInputProps: getFilesInputProps,
  } = useDropzone({
    accept: ALL_ACCEPTED_FILE_TYPES,
    maxSize: 100 * 1024 * 1024, // 100MB max for any file
    multiple: true, // Allow multiple files
    onDrop: (acceptedFiles) => {
      // Validate file types
      const validFiles = acceptedFiles.filter(file => {
        const fileType = detectFileType(file);
        return fileType === 'image' || fileType === 'video';
      });

      if (validFiles.length !== acceptedFiles.length) {
        toast.error('Some files were rejected. Only images and videos are supported.');
      }

      // Separate files by type
      const { images, videos } = separateFilesByType(validFiles);

      // Validate multiple images / single video rule
      if (videos.length > 1) {
        toast.error('Only one video file is allowed per collection.');
        return;
      }

      // Combine existing files with new ones, but enforce video limit
      const existingFiles = files || [];
      const { images: existingImages, videos: existingVideos } = separateFilesByType(existingFiles);

      // Check if adding new video when one already exists
      if (videos.length > 0 && existingVideos.length > 0) {
        toast.error('Only one video file is allowed per collection. Please remove the existing video first.');
        return;
      }

      // Combine files: all images + max 1 video
      const finalVideos = videos.length > 0 ? videos : existingVideos;
      const finalImages = [...existingImages, ...images];
      const finalFiles = [...finalImages, ...finalVideos];

      setFiles(finalFiles);
      setValue('files', finalFiles);
    },
    onDropRejected: (rejectedFiles) => {
      if (rejectedFiles[0]?.errors[0]?.code === 'file-too-large') {
        toast.error('File is too large. Maximum size is 100MB per file.');
      } else {
        toast.error('Please upload valid image or video files.');
      }
    },
  });

  // Function to remove a specific file
  const removeFile = (indexToRemove) => {
    const updatedFiles = files.filter((_, index) => index !== indexToRemove);
    setFiles(updatedFiles);
    setValue('files', updatedFiles);
  };

  const onSubmit = async (data) => {
    if (!data.files || data.files.length === 0) {
      toast.error('Please upload at least one file');
      return;
    }

    // Validate file type constraints
    const { videos } = separateFilesByType(data.files);
    if (videos.length > 1) {
      toast.error('Only one video file is allowed per collection.');
      return;
    }

    try {
      // Prepare collection data for new API structure
      const collectionData = {
        title: data.title,
        description: data.description || '',
        amount: parseFloat(data.price),
        banner: data.banner || null, // Will be converted to base64 in useCollections
      };

      // Create collection with files using the updated workflow
      await createCollectionWithFiles(collectionData, data.files);

      toast.success('Collection created and published successfully!');

      // Reset form
      setBannerPreview(null);
      setFiles([]);
      reset();

    } catch (error) {
      console.error('Collection creation error:', error);
      toast.error('Failed to create collection. Please try again.');
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <div className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-300">
            Resource Title*
          </label>
          <div className="mt-1">
            <input
              id="title"
              type="text"
              className="input-glass w-full"
              placeholder="Enter a title for your resource"
              {...register('title', { required: 'Title is required' })}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-500">{errors.title.message}</p>
            )}
          </div>
        </div>
        
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-300">
            Description
          </label>
          <div className="mt-1">
            <textarea
              id="description"
              rows="4"
              className="input-glass w-full"
              placeholder="Describe your resource (optional)..."
              {...register('description')}
            ></textarea>
            {errors.description && (
              <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>
        </div>
        
        <div>
          <label htmlFor="price" className="block text-sm font-medium text-gray-300">
            Price (USD)*
          </label>
          <div className="mt-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500">$</span>
            </div>
            <input
              id="price"
              type="number"
              min="0"
              step="0.01"
              className="input-glass w-full pl-7"
              placeholder="0.00"
              {...register('price', {
                required: 'Price is required',
                min: { value: 0, message: 'Price cannot be negative' },
                pattern: {
                  value: /^\d+(\.\d{1,2})?$/,
                  message: 'Price must be a valid number with up to 2 decimal places',
                },
              })}
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-500">{errors.price.message}</p>
            )}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Banner Image
          </label>
          <div {...getBannerRootProps()} className="cursor-pointer">
            <input {...getBannerInputProps()} />
            <div
              className={`border-2 border-dashed rounded-xl p-4 text-center transition-colors ${
                bannerPreview
                  ? 'border-primary-500/50 bg-primary-500/5'
                  : 'border-gray-700 hover:border-gray-500 bg-gray-800/30'
              }`}
            >
              {bannerPreview ? (
                <div className="relative">
                  <img
                    src={bannerPreview}
                    alt="Banner preview"
                    className="mx-auto max-h-48 rounded-lg object-contain"
                  />
                  <p className="mt-2 text-sm text-gray-400">Click or drag to replace</p>
                </div>
              ) : (
                <div className="py-4">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                    aria-hidden="true"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <p className="mt-1 text-sm text-gray-400">
                    Click or drag and drop to upload a banner image (optional)
                  </p>
                  <p className="mt-1 text-xs text-gray-500">PNG, JPG, GIF, WEBP up to 5MB</p>
                </div>
              )}
            </div>
            {errors.banner && (
              <p className="mt-1 text-sm text-red-500">{errors.banner.message}</p>
            )}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Resource Files*
          </label>
          <div
            {...getFilesRootProps()}
            className={`border-2 border-dashed rounded-xl p-4 text-center cursor-pointer transition-colors ${
              files.length > 0
                ? 'border-primary-500/50 bg-primary-500/5'
                : 'border-gray-700 hover:border-gray-500 bg-gray-800/30'
            }`}
          >
            <input {...getFilesInputProps()} />
            {files.length > 0 ? (
              <div>
                {(() => {
                  const { images, videos } = separateFilesByType(files);
                  return (
                    <div className="space-y-4">
                      {/* Images Section */}
                      {images.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-300 mb-2">
                            Images ({images.length})
                          </h4>
                          <ul className="space-y-2">
                            {images.map((file, index) => {
                              const originalIndex = files.findIndex(f => f === file);
                              return (
                                <li
                                  key={`image-${file.name}-${index}`}
                                  className="flex items-center justify-between glass-dark p-2 rounded-lg"
                                >
                                  <div className="flex items-center space-x-2">
                                    <span className="px-2 py-1 text-xs rounded bg-green-500/20 text-green-400">
                                      IMG
                                    </span>
                                    <span className="text-sm truncate max-w-xs">{file.name}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <span className="text-xs text-gray-400">{formatFileSize(file.size)}</span>
                                    <button
                                      type="button"
                                      onClick={() => removeFile(originalIndex)}
                                      className="text-red-400 hover:text-red-300 text-xs"
                                    >
                                      ✕
                                    </button>
                                  </div>
                                </li>
                              );
                            })}
                          </ul>
                        </div>
                      )}

                      {/* Videos Section */}
                      {videos.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-300 mb-2">
                            Video ({videos.length}/1)
                          </h4>
                          <ul className="space-y-2">
                            {videos.map((file, index) => {
                              const originalIndex = files.findIndex(f => f === file);
                              return (
                                <li
                                  key={`video-${file.name}-${index}`}
                                  className="flex items-center justify-between glass-dark p-2 rounded-lg"
                                >
                                  <div className="flex items-center space-x-2">
                                    <span className="px-2 py-1 text-xs rounded bg-blue-500/20 text-blue-400">
                                      VID
                                    </span>
                                    <span className="text-sm truncate max-w-xs">{file.name}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <span className="text-xs text-gray-400">{formatFileSize(file.size)}</span>
                                    <button
                                      type="button"
                                      onClick={() => removeFile(originalIndex)}
                                      className="text-red-400 hover:text-red-300 text-xs"
                                    >
                                      ✕
                                    </button>
                                  </div>
                                </li>
                              );
                            })}
                          </ul>
                        </div>
                      )}
                    </div>
                  );
                })()}
                <p className="mt-4 text-sm text-gray-400">
                  Click or drag to add more files (multiple images, max 1 video)
                </p>
              </div>
            ) : (
              <div className="py-4">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                  aria-hidden="true"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="mt-1 text-sm text-gray-400">
                  Click or drag and drop to upload your resource files
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  <span className="font-medium">Images:</span> JPG, PNG, GIF, WEBP (multiple allowed, optimized upload)<br />
                  <span className="font-medium">Videos:</span> MP4, MOV, AVI, MKV, WEBM (max 1 per collection)<br />
                  <span className="text-gray-400">Up to 100MB per file</span>
                </p>
              </div>
            )}
          </div>
          {errors.files && (
            <p className="mt-1 text-sm text-red-500">{errors.files.message}</p>
          )}
        </div>
      </div>
      
      {/* Progress bar */}
      {isLoading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-400">
            <span>
              {currentProgress < 10 ? 'Creating collection...' :
               currentProgress < 90 ? 'Uploading files (images: direct, videos: multipart)...' :
               'Publishing collection...'}
            </span>
            <span>{Math.round(currentProgress)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${currentProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row items-center justify-end space-y-3 sm:space-y-0 sm:space-x-4">
        <Link to="/explore" className="btn-glass w-full sm:w-auto">
          Cancel
        </Link>
        <button
          type="submit"
          className="btn-primary w-full sm:w-auto flex justify-center items-center space-x-2"
          disabled={isLoading}
        >
          {isLoading && (
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          )}
          <span>{isLoading ? 'Creating Collection...' : 'Create Collection'}</span>
        </button>
      </div>
    </form>
  );
};

export default UploadForm;